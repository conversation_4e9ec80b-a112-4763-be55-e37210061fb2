# 更新日誌 (繁體中文)

本文件記錄了 **MCP Feedback Enhanced** 的所有版本更新內容。

## [v2.2.5] - WSL 環境支援與跨平台增強

### ✨ 新功能
- 🐧 **WSL 環境檢測**: 自動識別 WSL 環境，提供專門的支援邏輯
- 🌐 **智能瀏覽器啟動**: WSL 環境下自動調用 Windows 瀏覽器，支援多種啟動方式
- 🔧 **跨平台測試增強**: 測試功能整合 WSL 檢測，提升測試覆蓋率

### 🚀 改進功能
- 🎯 **環境檢測優化**: 改進遠端環境檢測邏輯，WSL 不再被誤判為遠端環境
- 📊 **系統資訊增強**: 系統資訊工具新增 WSL 環境狀態顯示
- 🧪 **測試體驗提升**: 測試模式下自動嘗試啟動瀏覽器，提供更好的測試體驗

---

## [v2.2.4] - GUI 體驗優化與問題修復

### 🐛 問題修復
- 🖼️ **圖片重複貼上修復**: 解決 GUI 介面中使用 Ctrl+V 複製貼上圖片時出現重複貼上的問題
- 🌐 **語系切換修復**: 修復圖片設定區域在語言切換時文字沒有正確翻譯的問題
- 📝 **字體可讀性改善**: 調整圖片設定區域的字體大小，提升文字可讀性

---

## [v2.2.3] - 超時控制與圖片設定增強

### ✨ 新功能
- ⏰ **用戶超時控制**: 新增可自訂的超時設定功能，支援 30 秒至 2 小時的彈性設定
- ⏱️ **倒數計時器**: 介面頂部顯示即時倒數計時器，提供視覺化的時間提醒
- 🖼️ **圖片大小限制**: 新增圖片上傳大小限制設定（無限制/1MB/3MB/5MB）
- 🔧 **Base64 相容模式**: 新增 Base64 詳細模式，提升部分 AI 模型的圖片識別相容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 腳本，協助管理和清理 UV cache 空間

### 🚀 改進功能
- 📚 **文檔結構優化**: 重新整理文檔目錄結構，將圖片移至 `docs/{語言}/images/` 路徑
- 📖 **Cache 管理指南**: 新增詳細的 UV Cache 管理指南，包含自動化清理方案
- 🎯 **智能相容性提示**: 當圖片上傳失敗時自動顯示 Base64 相容模式建議

### 🐛 問題修復
- 🛡️ **超時處理優化**: 改進用戶自訂超時與 MCP 系統超時的協調機制
- 🖥️ **介面自動關閉**: 修復超時後介面自動關閉和資源清理邏輯
- 📱 **響應式佈局**: 優化超時控制元件在小螢幕設備上的顯示效果

---

## [v2.2.2] - 超時自動清理修復

### 🐛 問題修復
- 🔄 **超時自動清理**: 修復 GUI/Web UI 在 MCP session timeout (預設 600 秒) 後沒有自動關閉的問題
- 🛡️ **資源管理優化**: 改進超時處理機制，確保在超時時正確清理和關閉所有 UI 資源
- ⚡ **超時檢測增強**: 加強超時檢測邏輯，確保在各種情況下都能正確處理超時事件

---

## [v2.2.1] - 視窗優化與統一設定接口

### 🚀 改進功能
- 🖥️ **視窗大小限制解除**: 解除 GUI 主視窗最小大小限制，從 1000×800 降至 400×300
- 💾 **視窗狀態實時保存**: 實現視窗大小與位置的即時保存機制，支援防抖延遲
- ⚙️ **統一設定接口優化**: 改進 GUI 設定版面的配置保存邏輯，避免設定衝突

### 🐛 問題修復
- 🔧 **視窗大小限制**: 解決 GUI 視窗無法調整至小尺寸的問題
- 🛡️ **設定衝突**: 修復設定保存時可能出現的配置衝突問題

---

## [v2.2.0] - 佈局與設定界面優化

### ✨ 新功能
- 🎨 **水平佈局模式**: GUI 與 Web UI 的合併模式新增摘要與回饋的左右佈局選項

### 🚀 改進功能
- 🎨 **設定界面改進**: 優化了 GUI 與 Web UI 的設定頁面，提升佈局清晰度
- ⌨️ **快捷鍵完善**: 提交回饋快捷鍵現已完整支援數字鍵盤的 Enter 鍵

### 🐛 問題修復
- 🔧 **圖片重複貼上**: 解決了在 Web UI 文字輸入區使用 Ctrl+V 貼上圖片時的重複問題

---

## [v2.1.1] - 視窗定位優化

### ✨ 新功能
- 🖥️ **智能視窗定位**: 新增「總是在主螢幕中心顯示視窗」設定選項
- 🌐 **多螢幕支援**: 完美解決 T 字型螢幕排列等複雜多螢幕環境的視窗定位問題
- 💾 **位置記憶**: 自動保存和恢復視窗位置，智能檢測視窗可見性

---

## [v2.1.0] - 全面重構版

### 🎨 重大重構
- 🏗️ **全面重構**: GUI 和 Web UI 採用模組化架構
- 📁 **集中管理**: 重新組織資料夾結構，提升維護性
- 🖥️ **界面優化**: 現代化設計和改進的用戶體驗

### ✨ 新功能
- 🍎 **macOS 界面優化**: 針對 macOS 用戶體驗進行專項改進
- ⚙️ **功能增強**: 新增設定選項和自動關閉頁面功能
- ℹ️ **關於頁面**: 新增關於頁面，包含版本資訊、專案連結和致謝內容

---

## [v2.0.14] - 快捷鍵與圖片功能增強

### 🚀 改進功能
- ⌨️ **增強快捷鍵**: Ctrl+Enter 支援數字鍵盤
- 🖼️ **智能圖片貼上**: Ctrl+V 直接貼上剪貼簿圖片

---

## [v2.0.9] - 多語言架構重構

### 🔄 重構
- 🌏 **多語言架構重構**: 支援動態載入
- 📁 **語言檔案模組化**: 模組化組織語言檔案

---

## [v2.0.3] - 編碼問題修復

### 🐛 重要修復
- 🛡️ **完全修復中文字符編碼問題**: 解決所有中文顯示相關問題
- 🔧 **解決 JSON 解析錯誤**: 修復資料解析錯誤

---

## [v2.0.0] - Web UI 支援 (2024-09-XX)

### 🌟 重大功能
- ✅ **新增 Web UI 支援**: 支援遠端環境使用
- ✅ **自動環境檢測**: 自動選擇合適的界面
- ✅ **WebSocket 即時通訊**: 實現即時雙向通訊

---

## 圖例說明

| 圖示 | 意義 |
|------|------|
| 🌟 | 版本亮點 |
| ✨ | 新功能 |
| 🚀 | 改進功能 |
| 🐛 | 問題修復 |
| 🔄 | 重構變更 |
| 🎨 | 界面優化 |
| ⚙️ | 設定相關 |
| 🖥️ | 視窗相關 |
| 🌐 | 多語言/網路相關 |
| 📁 | 檔案結構 |
| ⌨️ | 快捷鍵 |
| 🖼️ | 圖片功能 |

---

**完整專案資訊：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced) 